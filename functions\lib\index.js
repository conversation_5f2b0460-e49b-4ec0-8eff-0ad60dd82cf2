"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWalletData = exports.getListingById = exports.getListings = exports.fixAdminUser = exports.configureWalletSettings = exports.getWalletSettings = exports.verifyAdminPin = exports.setAdminPin = exports.markDeliveryCompleted = exports.releaseFundsWithCode = exports.releaseEscrowWithCode = exports.testEssential = exports.essentialWebhook = void 0;
// Minimal functions index - only essential webhook functionality
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const crypto_1 = require("crypto");
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
admin.initializeApp();
// CORS configuration for development and production
const corsHandler = (0, cors_1.default)({
    origin: [
        'https://h1c1-798a8.web.app',
        'https://h1c1-798a8.firebaseapp.com',
        'https://hivecampus.app',
        'https://www.hivecampus.app',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:3000',
        'http://localhost:5000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
// Import simple admin notification functions
// export * from './simple-admin-notifications'; // Temporarily disabled to fix deployment timeout
// Import notification functions
// export * from './notifications'; // Temporarily disabled to fix deployment timeout
// Import broadcast notification functions
// export * from './broadcast-notifications'; // Temporarily disabled due to deployment issues
// Helper function to generate 6-digit secret code
function generateSecretCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
// Essential Stripe webhook - only handles payment completion
exports.essentialWebhook = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    var _a, _b, _c;
    try {
        console.log('🔗 Essential webhook received');
        if (req.method !== 'POST') {
            res.status(405).send('Method not allowed');
            return;
        }
        const event = req.body;
        console.log(`📨 Event type: ${event.type}`);
        // Handle checkout session completed
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object;
            const metadata = session.metadata;
            if (metadata === null || metadata === void 0 ? void 0 : metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`📦 Processing order: ${orderId}`);
                // Get order
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    const orderData = orderDoc.data();
                    // Generate secret code
                    const secretCode = generateSecretCode();
                    console.log(`🔐 Generated code: ${secretCode}`);
                    // Update order
                    await orderRef.update({
                        status: 'payment_succeeded',
                        secretCode: secretCode,
                        paymentCompletedAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    // Update listing to sold
                    if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
                        await admin.firestore().collection('listings').doc(orderData.listingId).update({
                            status: 'sold',
                            soldAt: admin.firestore.Timestamp.now(),
                            updatedAt: admin.firestore.Timestamp.now()
                        });
                        console.log(`✅ Listing ${orderData.listingId} marked as sold`);
                    }
                    // Send buyer notification using new notification system
                    if (orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) {
                        try {
                            // Create in-app notification directly (since we're in the backend)
                            await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                                type: 'payment_success',
                                title: 'Payment Successful!',
                                message: `Your payment has been processed. Secret code: ${secretCode}`,
                                icon: '/icons/icon-192.png',
                                createdAt: admin.firestore.Timestamp.now(),
                                read: false,
                                link: `/orders/${orderId}`,
                                priority: 'high',
                                actionRequired: false,
                                metadata: {},
                                orderId: orderId,
                                secretCode: secretCode,
                                amount: orderData.totalAmount
                            });
                            // Also send push notification if user has FCM token
                            const tokenDoc = await admin.firestore()
                                .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                                .get();
                            if (tokenDoc.exists && ((_a = tokenDoc.data()) === null || _a === void 0 ? void 0 : _a.active) && ((_b = tokenDoc.data()) === null || _b === void 0 ? void 0 : _b.token)) {
                                const payload = {
                                    token: tokenDoc.data().token,
                                    notification: {
                                        title: 'Payment Successful!',
                                        body: `Your payment has been processed. Secret code: ${secretCode}`,
                                        imageUrl: '/icons/icon-192.png'
                                    },
                                    data: {
                                        type: 'payment_success',
                                        link: `/orders/${orderId}`,
                                        orderId: orderId,
                                        requireInteraction: 'true'
                                    },
                                    webpush: {
                                        headers: {
                                            TTL: '86400',
                                            Urgency: 'high'
                                        },
                                        notification: {
                                            icon: '/icons/icon-192.png',
                                            badge: '/icons/icon-96.png',
                                            tag: 'payment_success',
                                            requireInteraction: true,
                                            actions: [
                                                { action: 'view', title: 'View Order' },
                                                { action: 'dismiss', title: 'Dismiss' }
                                            ],
                                            vibrate: [200, 100, 200]
                                        }
                                    }
                                };
                                try {
                                    await admin.messaging().send(payload);
                                    console.log(`Push notification sent for payment success to user ${orderData.buyerId}`);
                                }
                                catch (pushError) {
                                    console.error('Error sending push notification:', pushError);
                                    // Mark token as inactive if it's invalid
                                    if (pushError.code === 'messaging/registration-token-not-registered') {
                                        await admin.firestore()
                                            .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                                            .update({ active: false });
                                    }
                                }
                            }
                        }
                        catch (notificationError) {
                            console.error('Error sending payment success notification:', notificationError);
                            // Don't fail the webhook if notification fails
                        }
                    }
                    // Create admin notification for payment completion - temporarily disabled
                    // try {
                    //   if (orderData) {
                    //     // Get user data for better notification
                    //     const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                    //     const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                    //     const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';
                    //     await createAdminNotification(
                    //       'payment_completed',
                    //       'Payment Completed',
                    //       `${buyerName} completed payment of $${orderData.totalAmount}`,
                    //       {
                    //         userId: orderData.buyerId,
                    //         username: buyerName,
                    //         orderId: orderId,
                    //         amount: orderData.totalAmount,
                    //         metadata: {
                    //           secretCode: secretCode,
                    //           listingId: orderData.listingId,
                    //           sellerId: orderData.sellerId
                    //         },
                    //         actionUrl: `/admin/transactions?search=${orderId}`
                    //       }
                    //     );
                    //   }
                    // } catch (notificationError) {
                    //   console.error('Error creating admin notification:', notificationError);
                    //   // Don't fail the webhook if notification fails
                    // }
                    console.log(`✅ Order ${orderId} processed successfully`);
                }
            }
        }
        // Handle payment failures
        if (event.type === 'payment_intent.payment_failed') {
            const paymentIntent = event.data.object;
            const metadata = paymentIntent.metadata;
            if (metadata === null || metadata === void 0 ? void 0 : metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`❌ Payment failed for order: ${orderId}`);
                try {
                    // Get order
                    const orderRef = admin.firestore().collection('orders').doc(orderId);
                    const orderDoc = await orderRef.get();
                    if (orderDoc.exists) {
                        const orderData = orderDoc.data();
                        if (orderData) {
                            // Update order status
                            await orderRef.update({
                                status: 'payment_failed',
                                paymentFailedAt: admin.firestore.Timestamp.now(),
                                updatedAt: admin.firestore.Timestamp.now()
                            });
                            // Get user data for notification
                            const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                            const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                            const buyerName = (buyerData === null || buyerData === void 0 ? void 0 : buyerData.name) || (buyerData === null || buyerData === void 0 ? void 0 : buyerData.displayName) || ((_c = buyerData === null || buyerData === void 0 ? void 0 : buyerData.email) === null || _c === void 0 ? void 0 : _c.split('@')[0]) || 'User';
                            // Create admin notification for payment failure - temporarily disabled
                            // await createAdminNotification(
                            //   'payment_failed',
                            //   'Payment Failed',
                            //   `Payment failed for ${buyerName}'s order of $${orderData.totalAmount}`,
                            //   {
                            //     userId: orderData.buyerId,
                            //     username: buyerName,
                            //     orderId: orderId,
                            //     amount: orderData.totalAmount,
                            //     metadata: {
                            //       failureReason: paymentIntent.last_payment_error?.message || 'Unknown error',
                            //       listingId: orderData.listingId,
                            //       sellerId: orderData.sellerId
                            //     },
                            //     actionUrl: `/admin/transactions?search=${orderId}`
                            //   }
                            // );
                            console.log(`❌ Payment failure processed for order: ${orderId}`);
                        }
                    }
                }
                catch (error) {
                    console.error('Error processing payment failure:', error);
                }
            }
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('❌ Webhook error:', error);
        res.status(500).send('Webhook failed');
    }
});
// Test function
exports.testEssential = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Essential webhook working',
        testCode: generateSecretCode(),
        timestamp: new Date().toISOString()
    });
});
// Release funds with secret code (alias for compatibility)
exports.releaseEscrowWithCode = functions
    .https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId, secretCode } = data;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized');
        }
        // Verify secret code
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.secretCode) !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Update order status
        await orderRef.update({
            status: 'completed',
            fundsReleased: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Funds released for order: ${orderId}`);
        return {
            success: true,
            message: 'Funds released successfully'
        };
    }
    catch (error) {
        console.error('Error releasing funds:', error);
        throw error;
    }
});
// Release funds with secret code (new name)
exports.releaseFundsWithCode = functions
    .https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId, secretCode } = data;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized');
        }
        // Verify secret code
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.secretCode) !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Update order status
        await orderRef.update({
            status: 'completed',
            fundsReleased: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Funds released for order: ${orderId}`);
        return {
            success: true,
            message: 'Funds released successfully'
        };
    }
    catch (error) {
        console.error('Error releasing funds:', error);
        throw error;
    }
});
// Mark delivery completed (for sellers)
exports.markDeliveryCompleted = functions
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify seller
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized');
        }
        // Update order status to delivered
        await orderRef.update({
            status: 'delivered',
            deliveredAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Notify buyer using new notification system
        if (orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) {
            try {
                // Create in-app notification
                await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                    type: 'order_delivered',
                    title: 'Order Delivered!',
                    message: `Your order has been delivered. Enter the secret code to release funds.`,
                    icon: '/icons/icon-192.png',
                    createdAt: admin.firestore.Timestamp.now(),
                    read: false,
                    link: `/orders/${orderId}`,
                    priority: 'high',
                    actionRequired: true,
                    metadata: {},
                    orderId: orderId
                });
                // Send push notification if available
                const tokenDoc = await admin.firestore()
                    .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                    .get();
                if (tokenDoc.exists && ((_a = tokenDoc.data()) === null || _a === void 0 ? void 0 : _a.active) && ((_b = tokenDoc.data()) === null || _b === void 0 ? void 0 : _b.token)) {
                    const payload = {
                        token: tokenDoc.data().token,
                        notification: {
                            title: 'Order Delivered!',
                            body: 'Your order has been delivered. Enter the secret code to release funds.',
                            imageUrl: '/icons/icon-192.png'
                        },
                        data: {
                            type: 'order_delivered',
                            link: `/orders/${orderId}`,
                            orderId: orderId,
                            requireInteraction: 'true'
                        },
                        webpush: {
                            headers: {
                                TTL: '86400',
                                Urgency: 'high'
                            },
                            notification: {
                                icon: '/icons/icon-192.png',
                                badge: '/icons/icon-96.png',
                                tag: 'order_delivered',
                                requireInteraction: true,
                                actions: [
                                    { action: 'view', title: 'Release Funds' },
                                    { action: 'dismiss', title: 'Dismiss' }
                                ],
                                vibrate: [200, 100, 200]
                            }
                        }
                    };
                    try {
                        await admin.messaging().send(payload);
                        console.log(`Push notification sent for order delivery to user ${orderData.buyerId}`);
                    }
                    catch (pushError) {
                        console.error('Error sending delivery push notification:', pushError);
                        if (pushError.code === 'messaging/registration-token-not-registered') {
                            await admin.firestore()
                                .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                                .update({ active: false });
                        }
                    }
                }
            }
            catch (notificationError) {
                console.error('Error sending delivery notification:', notificationError);
            }
        }
        console.log(`✅ Order ${orderId} marked as delivered`);
        return {
            success: true,
            message: 'Order marked as delivered'
        };
    }
    catch (error) {
        console.error('Error marking delivery:', error);
        throw error;
    }
});
// Function to set admin PIN
exports.setAdminPin = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can set PIN');
        }
        // Hash the PIN for security
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        // Store the hashed PIN in admin settings
        await admin.firestore().collection('adminSettings').doc('security').set({
            adminPin: hashedPin,
            pinSetAt: admin.firestore.Timestamp.now(),
            pinSetBy: context.auth.uid
        }, { merge: true });
        console.log(`Admin PIN set by user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Admin PIN set successfully'
        };
    }
    catch (error) {
        console.error('Error setting admin PIN:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set admin PIN', error);
    }
});
// Function to verify admin PIN
exports.verifyAdminPin = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can verify PIN');
        }
        // Get stored PIN hash
        const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
        if (!securityDoc.exists || !((_b = securityDoc.data()) === null || _b === void 0 ? void 0 : _b.adminPin)) {
            throw new functions.https.HttpsError('not-found', 'Admin PIN not set. Please set up your PIN first.');
        }
        // Hash the provided PIN and compare
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        const storedPin = (_c = securityDoc.data()) === null || _c === void 0 ? void 0 : _c.adminPin;
        if (hashedPin !== storedPin) {
            throw new functions.https.HttpsError('permission-denied', 'wrong pin unauthorised entry');
        }
        // Update last access time
        await admin.firestore().collection('users').doc(context.auth.uid).update({
            lastAdminAccess: admin.firestore.Timestamp.now()
        });
        console.log(`Admin PIN verified for user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'PIN verified successfully'
        };
    }
    catch (error) {
        console.error('Error verifying admin PIN:', error);
        throw error;
    }
});
// Helper function to get wallet configuration
async function getWalletConfig() {
    const settingsDoc = await admin.firestore().collection('adminSettings').doc('wallet').get();
    if (settingsDoc.exists) {
        const data = settingsDoc.data();
        return {
            signupBonus: (data === null || data === void 0 ? void 0 : data.signupBonus) || 0,
            referralBonus: (data === null || data === void 0 ? void 0 : data.referralBonus) || 0,
            enableSignupBonus: (data === null || data === void 0 ? void 0 : data.enableSignupBonus) || false,
            enableReferralBonus: (data === null || data === void 0 ? void 0 : data.enableReferralBonus) || false
        };
    }
    return {
        signupBonus: 0,
        referralBonus: 0,
        enableSignupBonus: false,
        enableReferralBonus: false
    };
}
// Get wallet settings (admin only)
exports.getWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const settings = await getWalletConfig();
        return {
            success: true,
            settings
        };
    }
    catch (error) {
        console.error('Error in getWalletSettings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Configure wallet settings (admin only)
exports.configureWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = data;
        // Validate input
        if (typeof signupBonus !== 'number' || signupBonus < 0 || signupBonus > 100) {
            throw new functions.https.HttpsError('invalid-argument', 'Signup bonus must be between 0 and 100');
        }
        if (typeof referralBonus !== 'number' || referralBonus < 0 || referralBonus > 100) {
            throw new functions.https.HttpsError('invalid-argument', 'Referral bonus must be between 0 and 100');
        }
        // Update settings
        await admin.firestore().collection('adminSettings').doc('wallet').set({
            signupBonus,
            referralBonus,
            enableSignupBonus: Boolean(enableSignupBonus),
            enableReferralBonus: Boolean(enableReferralBonus),
            updatedAt: admin.firestore.Timestamp.now(),
            updatedBy: context.auth.uid
        }, { merge: true });
        console.log(`Wallet settings updated by admin: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Wallet settings updated successfully'
        };
    }
    catch (error) {
        console.error('Error configuring wallet settings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Fix admin user function (for setup)
exports.fixAdminUser = functions.https.onCall(async (data, _context) => {
    try {
        const { email } = data;
        if (!email) {
            throw new functions.https.HttpsError('invalid-argument', 'Email is required');
        }
        // Get user by email
        const userRecord = await admin.auth().getUserByEmail(email);
        // Set custom claims with both admin and role
        await admin.auth().setCustomUserClaims(userRecord.uid, {
            admin: true,
            role: 'admin'
        });
        // Update or create user profile in Firestore with complete admin setup
        await admin.firestore().collection('users').doc(userRecord.uid).set({
            uid: userRecord.uid,
            name: userRecord.displayName || 'Admin User',
            email: userRecord.email,
            role: 'admin',
            university: 'Hive Campus Admin',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
            emailVerified: true,
            status: 'active',
            adminLevel: 'super',
            permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
        }, { merge: true });
        console.log(`Admin user fixed for: ${email}`);
        return {
            success: true,
            message: `Admin user fixed for ${email}`,
            uid: userRecord.uid
        };
    }
    catch (error) {
        console.error('Error fixing admin user:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fix admin user', error);
    }
});
// Helper function to verify authentication
function verifyAuth(context) {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }
    return context.auth;
}
// Get listings with filtering
exports.getListings = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        verifyAuth(context);
        const { university, category, type, condition, minPrice, maxPrice, ownerId, status = 'active', limit = 20, lastVisible } = data;
        // Get current user's university for visibility filtering
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        const currentUserUniversity = (_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.university;
        let query = admin.firestore().collection('listings')
            .where('status', '==', status)
            .orderBy('createdAt', 'desc');
        // Apply filters if provided
        if (university) {
            query = query.where('university', '==', university);
        }
        if (category) {
            query = query.where('category', '==', category);
        }
        if (type) {
            query = query.where('type', '==', type);
        }
        if (condition) {
            query = query.where('condition', '==', condition);
        }
        if (ownerId) {
            query = query.where('ownerId', '==', ownerId);
        }
        // Apply limit
        query = query.limit(limit);
        // Apply pagination if lastVisible is provided
        if (lastVisible) {
            const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
            if (lastDoc.exists) {
                query = query.startAfter(lastDoc);
            }
        }
        const snapshot = await query.get();
        const listings = [];
        let lastVisibleId = null;
        snapshot.forEach(doc => {
            const listing = doc.data();
            // Apply visibility filtering
            const isVisible = listing.visibility === 'public' ||
                (listing.visibility === 'university' && listing.university === currentUserUniversity);
            if (!isVisible) {
                return; // Skip this listing
            }
            // Apply price filtering in memory (can't do range queries along with other filters in Firestore)
            const price = listing.price;
            if ((minPrice === undefined || price >= minPrice) &&
                (maxPrice === undefined || price <= maxPrice)) {
                listings.push(Object.assign({ id: doc.id }, listing));
            }
            // Set the last visible document ID for pagination
            lastVisibleId = doc.id;
        });
        return {
            success: true,
            data: {
                listings,
                lastVisible: lastVisibleId,
                total: listings.length
            }
        };
    }
    catch (error) {
        console.error('Error getting listings:', error);
        throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
    }
});
// Get a single listing by ID
exports.getListingById = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        verifyAuth(context);
        const { listingId } = data;
        if (!listingId) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
        }
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        // Don't return deleted listings unless it's the owner
        if ((listing === null || listing === void 0 ? void 0 : listing.status) === 'deleted' && listing.ownerId !== ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid)) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        return {
            success: true,
            data: Object.assign({ id: listingDoc.id }, listing)
        };
    }
    catch (error) {
        console.error('Error getting listing by ID:', error);
        throw error;
    }
});
// Get wallet balance and history
exports.getWalletData = functions.https.onCall(async (_data, context) => {
    try {
        verifyAuth(context);
        const userId = context.auth.uid;
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            // Initialize wallet if it doesn't exist
            const walletData = {
                userId,
                balance: 0,
                referralCode: `user${userId.substring(0, 6)}`,
                usedReferral: false,
                history: [],
                grantedBy: 'system',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            };
            await admin.firestore().collection('wallets').doc(userId).set(walletData);
            return walletData;
        }
        return walletDoc.data();
    }
    catch (error) {
        console.error('Error getting wallet data:', error);
        throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
    }
});
