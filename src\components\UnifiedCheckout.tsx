import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  ArrowLeft,
  ArrowRight,
  Package,
  MapPin,
  CreditCard,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { loadStripe } from '@stripe/stripe-js';

import { useAuth } from '../contexts/AuthContext';
import { useListings } from '../hooks/useListings';
import { useAddressManagement } from '../hooks/useAddressManagement';
import HiveCampusLoader from './HiveCampusLoader';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Listing, UserAddress } from '../firebase/types';
import { validateListingForCheckout, canPurchaseListing, formatValidationErrors } from '../utils/listingValidation';
import AddressManagement from './AddressManagement';
import { formatPrice } from '../utils/priceUtils';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import WalletCreditSelector from './checkout/WalletCreditSelector';
import {
  getShippingRates,
  convertListingToPackageDetails,
  convertSellerAddress,
  convertUserAddress,
  getCheapestRate,
  ShippingRate
} from '../services/shippingService';

// Initialize Stripe with live publishable key
// Note: The HTTPS warning in development is expected and doesn't affect functionality
// Stripe.js works over HTTP in development but requires HTTPS in production
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

interface CheckoutAction {
  type: 'buy' | 'rent' | 'bid';
  price: number;
  period?: 'weekly' | 'monthly';
  bidAmount?: number;
}

const UnifiedCheckout: React.FC = () => {
  const { listingId } = useParams<{ listingId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { fetchListing } = useListings();
  const { addresses, getDefaultAddress } = useAddressManagement();
  const { createCheckoutSession: _createCheckoutSession } = useStripeCheckout();

  // State management
  const [currentStep, setCurrentStep] = useState(1);
  const [listing, setListing] = useState<Listing | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<UserAddress | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Wallet state
  const [walletBalance, setWalletBalance] = useState(0);
  const [appliedWalletCredit, setAppliedWalletCredit] = useState(0);
  const [isLoadingWallet, setIsLoadingWallet] = useState(true);

  // Shipping state
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const [selectedShippingRate, setSelectedShippingRate] = useState<ShippingRate | null>(null);
  const [isLoadingShippingRates, setIsLoadingShippingRates] = useState(false);
  const [shippingRatesError, setShippingRatesError] = useState<string | null>(null);

  // Get checkout action from location state or URL params
  const [checkoutAction, setCheckoutAction] = useState<CheckoutAction>({
    type: 'buy',
    price: 0
  });



  // Load wallet balance
  const loadWalletBalance = useCallback(async () => {
    try {
      setIsLoadingWallet(true);
      const getWalletData = httpsCallable(functions, 'getWalletData');
      const result = await getWalletData({});
      const walletData = result.data as any;
      setWalletBalance(walletData.balance || 0);
    } catch (error) {
      console.error('Error loading wallet balance:', error);
      setWalletBalance(0);
    } finally {
      setIsLoadingWallet(false);
    }
  }, []);

  // Load shipping rates when address and listing are available
  const loadShippingRates = useCallback(async () => {
    if (!listing || !selectedAddress || listing.deliveryMethod !== 'mail' || listing.shippingOptions?.model !== 'shippo') {
      setShippingRates([]);
      setSelectedShippingRate(null);
      return;
    }

    try {
      setIsLoadingShippingRates(true);
      setShippingRatesError(null);

      // Convert listing data to required formats
      const packageDetails = convertListingToPackageDetails(listing);
      const sellerAddress = convertSellerAddress(listing);
      const buyerAddress = convertUserAddress(selectedAddress);

      if (!packageDetails || !sellerAddress) {
        throw new Error('Missing package details or seller address');
      }

      // Get real-time shipping rates
      const rates = await getShippingRates(sellerAddress, buyerAddress, packageDetails);

      setShippingRates(rates);

      // Auto-select the cheapest rate
      const cheapestRate = getCheapestRate(rates);
      if (cheapestRate) {
        setSelectedShippingRate(cheapestRate);
      }

    } catch (error: any) {
      console.error('Error loading shipping rates:', error);
      setShippingRatesError(error.message || 'Failed to load shipping rates');

      // Fallback to estimated rates
      const packageSize = listing?.shippingOptions?.packageSize || 'medium';
      const estimates = {
        small: 4.50,
        medium: 6.50,
        large: 9.00
      };
      const estimatedCost = estimates[packageSize as keyof typeof estimates];

      setShippingRates([{
        carrier: 'USPS',
        service: 'Ground (Estimated)',
        amount: estimatedCost,
        currency: 'USD',
        estimatedDays: 3,
        rateId: 'estimated'
      }]);

      setSelectedShippingRate({
        carrier: 'USPS',
        service: 'Ground (Estimated)',
        amount: estimatedCost,
        currency: 'USD',
        estimatedDays: 3,
        rateId: 'estimated'
      });
    } finally {
      setIsLoadingShippingRates(false);
    }
  }, [listing, selectedAddress]);

  // Optimized listing loading with error handling
  const loadListing = useCallback(async () => {
    if (!listingId) {
      setError('No listing ID provided');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log('UnifiedCheckout: Loading listing with ID:', listingId);
      const listingData = await fetchListing(listingId);
      if (!listingData) {
        console.error('UnifiedCheckout: Listing not found for ID:', listingId);
        setError('Listing not found');
        return;
      }

      console.log('UnifiedCheckout: Listing loaded successfully:', listingData);
      console.log('UnifiedCheckout: Listing ownerId:', listingData.ownerId);
      setListing(listingData);

      // Determine checkout action from location state or listing type
      const state = location.state as any;
      if (state?.action?.type && state?.action?.price > 0) {
        setCheckoutAction(state.action);
      } else {
        // Default to buy action with listing price
        setCheckoutAction({
          type: 'buy',
          price: listingData.price
        });
      }
    } catch (error: unknown) {
      console.error('Error loading listing:', error);
      setError(error instanceof Error ? error.message : 'Failed to load listing');
    } finally {
      setIsLoading(false);
    }
  }, [listingId]); // Remove fetchListing and location.state dependencies to prevent infinite re-renders

  // Calculate total - moved before early returns to fix hooks violation
  const calculateTotal = useMemo(() => {
    const basePrice = checkoutAction.price || 0;

    // Calculate shipping fee based on listing delivery method
    let shippingFee = 0;
    if (listing?.deliveryMethod === 'mail' && selectedAddress) {
      // Check who pays for shipping
      const shippingPaidBy = listing?.shippingOptions?.paidBy || 'buyer';

      if (shippingPaidBy === 'buyer') {
        // Use real-time shipping rate if available
        if (selectedShippingRate && selectedShippingRate.rateId !== 'estimated') {
          shippingFee = selectedShippingRate.amount;
        } else if (listing?.shippingOptions?.estimatedCost) {
          // Fallback to listing estimated cost
          shippingFee = listing.shippingOptions.estimatedCost;
        } else {
          // Final fallback to default estimates
          const packageSize = listing?.shippingOptions?.packageSize || 'medium';
          const estimates = {
            small: 4.50,
            medium: 6.50,
            large: 9.00
          };
          shippingFee = estimates[packageSize];
        }
      }
      // If seller pays, shipping fee is 0 for buyer
    }

    const subtotal = basePrice + shippingFee;

    // Use the applied wallet credit from the selector
    const walletCreditToApply = appliedWalletCredit;

    // Final amount to charge to Stripe (after wallet credit)
    const finalTotal = Math.max(0, subtotal - walletCreditToApply);

    return {
      basePrice: Math.round(basePrice * 100) / 100,
      shippingFee: Math.round(shippingFee * 100) / 100,
      subtotal: Math.round(subtotal * 100) / 100,
      walletCredit: Math.round(walletCreditToApply * 100) / 100,
      total: Math.round(finalTotal * 100) / 100,
      deliveryMethod: listing?.deliveryMethod || 'in_person',
      shippingPaidBy: listing?.shippingOptions?.paidBy || 'buyer'
    };
  }, [checkoutAction.price, selectedAddress, appliedWalletCredit, listing, selectedShippingRate]);

  // Dynamic steps based on delivery method - moved before early returns
  const steps = useMemo(() => {
    const baseSteps = [
      { number: 1, title: 'Review Item', icon: Package }
    ];

    // Only add shipping address step for mail delivery
    if (calculateTotal.deliveryMethod === 'mail') {
      baseSteps.push({ number: 2, title: 'Shipping Address', icon: MapPin });
      baseSteps.push({ number: 3, title: 'Order Confirmation', icon: CheckCircle });
      baseSteps.push({ number: 4, title: 'Secure Checkout', icon: CreditCard });
    } else {
      baseSteps.push({ number: 2, title: 'Order Confirmation', icon: CheckCircle });
      baseSteps.push({ number: 3, title: 'Secure Checkout', icon: CreditCard });
    }

    return baseSteps;
  }, [calculateTotal.deliveryMethod]);

  // Load listing data and wallet balance
  useEffect(() => {
    loadListing();
    loadWalletBalance();
  }, [listingId]); // Only depend on listingId to prevent infinite re-renders

  // Set default address when addresses are loaded
  useEffect(() => {
    if (addresses.length > 0 && !selectedAddress) {
      const defaultAddr = getDefaultAddress();
      if (defaultAddr) {
        setSelectedAddress(defaultAddr);
      }
    }
  }, [addresses.length, selectedAddress]); // Remove getDefaultAddress dependency to prevent re-renders

  // Load shipping rates when address or listing changes
  useEffect(() => {
    loadShippingRates();
  }, [loadShippingRates]);

  // Early return if listing is not loaded yet
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center overflow-x-hidden">
        <div className="flex flex-col items-center space-y-3">
          <HiveCampusLoader size="medium" />
          <span className="text-gray-600 dark:text-gray-400">Loading checkout...</span>
        </div>
      </div>
    );
  }

  if (error || !listing) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Checkout Error
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error || 'Unable to load checkout'}
          </p>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const handleNext = () => {
    console.log('handleNext called, currentStep:', currentStep, 'selectedAddress:', selectedAddress);

    // Validate current step before proceeding
    const isMailDelivery = calculateTotal.deliveryMethod === 'mail';

    // For mail delivery, validate shipping address on step 2
    if (isMailDelivery && currentStep === 2 && !selectedAddress) {
      setError('Please select a shipping address before continuing.');
      return;
    }

    const maxStep = steps.length;
    if (currentStep < maxStep) {
      setError(null); // Clear any previous errors
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigate(-1);
    }
  };

  const handleAddressSelect = (address: UserAddress) => {
    console.log('Address selected:', address);
    setSelectedAddress(address);
    setError(null); // Clear any previous errors when address is selected
  };

  const handleCheckout = async () => {
    const isMailDelivery = calculateTotal.deliveryMethod === 'mail';

    // Comprehensive listing validation
    const validation = validateListingForCheckout(listing);
    if (!validation.isValid) {
      console.error('❌ Listing validation failed:', validation.errors);
      setError(formatValidationErrors(validation.errors));
      return;
    }

    // Log any warnings
    if (validation.warnings.length > 0) {
      console.warn('⚠️ Listing validation warnings:', validation.warnings);
    }

    // Check if user can purchase this listing
    const purchaseCheck = canPurchaseListing(listing, currentUser?.uid || null);
    if (!purchaseCheck.canPurchase) {
      console.error('❌ Purchase validation failed:', purchaseCheck.reason);
      setError(purchaseCheck.reason || 'Cannot purchase this listing');
      return;
    }

    console.log('✅ Listing validation passed:', {
      listingId: listing?.id,
      ownerId: listing?.ownerId,
      userId: listing?.userId,
      title: listing?.title,
      currentUserId: currentUser?.uid
    });

    // For mail delivery, require shipping address
    if (isMailDelivery && !selectedAddress) {
      setError('Please select a shipping address for mail delivery.');
      return;
    }

    // For Shippo orders, require shipping rate selection
    if (isMailDelivery && listing?.shippingOptions?.model === 'shippo' && !selectedShippingRate) {
      setError('Please select a shipping option.');
      return;
    }

    // Validate checkout action
    if (!checkoutAction.price || checkoutAction.price <= 0) {
      setError('Invalid price for this item.');
      return;
    }

    // Validate rental period for rent orders
    if (checkoutAction.type === 'rent' && !checkoutAction.period) {
      setError('Please select a rental period.');
      return;
    }

    // Validate bid amount for auction orders
    if (checkoutAction.type === 'bid' && (!checkoutAction.bidAmount || checkoutAction.bidAmount <= 0)) {
      setError('Invalid bid amount.');
      return;
    }

    // Validate address fields only for mail delivery
    if (isMailDelivery && selectedAddress) {
      if (!selectedAddress.fullName || !selectedAddress.phone || !selectedAddress.line1 ||
          !selectedAddress.city || !selectedAddress.state || !selectedAddress.zipCode) {
        setError('Please complete all required address fields.');
        return;
      }
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Prepare order details for checkout
      const orderDetails = {
        type: checkoutAction.type,
        price: checkoutAction.price,
        period: checkoutAction.period,
        bidAmount: checkoutAction.bidAmount,
        deliveryMethod: calculateTotal.deliveryMethod,
        shippingFee: calculateTotal.shippingFee,
        shippingPaidBy: calculateTotal.shippingPaidBy,
        // Include shipping rate information for Shippo orders
        ...(selectedShippingRate && {
          shippingRate: {
            rateId: selectedShippingRate.rateId,
            carrier: selectedShippingRate.carrier,
            service: selectedShippingRate.service,
            amount: selectedShippingRate.amount,
            estimatedDays: selectedShippingRate.estimatedDays
          }
        }),
        // Only include shipping address for mail delivery
        ...(isMailDelivery && selectedAddress && {
          shippingAddress: {
            fullName: selectedAddress.fullName,
            phone: selectedAddress.phone,
            line1: selectedAddress.line1,
            line2: selectedAddress.line2,
            city: selectedAddress.city,
            state: selectedAddress.state,
            zipCode: selectedAddress.zipCode,
            country: selectedAddress.country,
          }
        })
      };

      // Process REAL Stripe checkout with live keys
      console.log('=== CHECKOUT DEBUG START ===');
      console.log('Creating real Stripe checkout session');
      console.log('Order details:', orderDetails);
      console.log('Pricing:', pricing);
      console.log('listingId from params:', listingId);
      console.log('listing object:', listing);

      console.log('Initializing Stripe...');
      const stripe = await stripePromise;
      console.log('Stripe initialized:', !!stripe);
      if (!stripe) {
        throw new Error('Stripe failed to initialize');
      }

      // Validate listingId before making the request
      const actualListingId = listingId || listing?.id;

      if (!actualListingId) {
        console.error('ERROR: No valid listing ID found');
        setError('Invalid listing ID. Please try again.');
        return;
      }

      console.log('Creating checkout session for listing:', actualListingId);

      // Try backend first, then fallback to direct Stripe integration
      try {
        const checkoutResponse = await _createCheckoutSession(actualListingId, appliedWalletCredit > 0, {
          ...orderDetails,
          appliedWalletCredit
        });

        console.log('Checkout response:', checkoutResponse);

        // Handle wallet-only payments (no Stripe redirect needed)
        if (checkoutResponse.paidWithWallet) {
          console.log('✅ Order paid with wallet balance only!');

          // Redirect to success page with order ID and secret code
          const params = new URLSearchParams({
            order_id: checkoutResponse.orderId,
            paid_with_wallet: 'true',
            wallet_amount: checkoutResponse.walletAmountUsed.toString(),
            ...(checkoutResponse.secretCode && { secret_code: checkoutResponse.secretCode })
          });

          window.location.href = `/order-success?${params.toString()}`;
          return;
        }

        // Handle regular Stripe payments
        if (checkoutResponse.sessionUrl) {
          console.log('Stripe session created via backend, redirecting to:', checkoutResponse.sessionUrl);
          // Redirect to actual Stripe checkout page
          window.location.href = checkoutResponse.sessionUrl;
        } else {
          throw new Error('Invalid checkout response - no session URL or wallet payment confirmation');
        }
      } catch (backendError) {
        console.error('Backend checkout failed:', backendError);

        // Log detailed error information for debugging
        if (backendError instanceof Error) {
          console.error('Error details:', {
            message: backendError.message,
            name: backendError.name,
            stack: backendError.stack
          });

          // Check for specific error types and provide better error messages
          if (backendError.message.includes('sellerId') || backendError.message.includes('seller information')) {
            setError('This listing has invalid seller information and cannot be purchased. Please contact the seller or support for assistance.');
            return;
          }

          if (backendError.message.includes('Listing not found')) {
            setError('This listing is no longer available.');
            return;
          }

          if (backendError.message.includes('Unauthorized') || backendError.message.includes('authentication')) {
            setError('Please log in again to complete your purchase.');
            return;
          }
        }

        // Show user-friendly error message based on error type
        if (backendError instanceof Error) {
          if (backendError.message.includes('Invalid listing ID')) {
            setError('❌ Listing not found. Please refresh the page and try again.');
          } else if (backendError.message.includes('Listing not found')) {
            setError('❌ This listing is no longer available.');
          } else if (backendError.message.includes('You cannot purchase your own listing')) {
            setError('❌ You cannot purchase your own listing.');
          } else if (backendError.message.includes('Listing is not available')) {
            setError('❌ This listing is no longer available for purchase.');
          } else if (backendError.message.includes('Failed to create Stripe session')) {
            setError('💳 Payment system error. Please try again in a moment. If the issue persists, contact <NAME_EMAIL>');
          } else if (backendError.message.includes('Failed to create checkout session')) {
            setError('💳 Unable to connect to payment processor. Please check your internet connection and try again. If the issue persists, contact <NAME_EMAIL>');
          } else {
            setError(`💳 Payment processing error: ${backendError.message}. Please try again or contact <NAME_EMAIL> if the issue persists.`);
          }
        } else {
          setError('💳 Payment processing is now live! There was a temporary connection issue. Please try again in a moment, or contact <NAME_EMAIL> if the issue persists.');
        }
        return;
      }
    } catch (err: any) {
      console.error('Checkout error:', err);

      // Handle specific error types
      if (err.message?.includes('address')) {
        setError('There was an issue with your shipping address. Please verify and try again.');
      } else if (err.message?.includes('payment')) {
        setError('Payment processing failed. Please check your payment method and try again.');
      } else if (err.message?.includes('listing')) {
        setError('This item is no longer available for purchase.');
      } else {
        setError('Failed to process checkout. Please try again or contact support if the issue persists.');
      }
    } finally {
      setIsProcessing(false);
    }
  };



  const pricing = calculateTotal;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 overflow-x-hidden">
      <div className="max-w-4xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBack}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white mb-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </button>
          
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Checkout
          </h1>
          
          {/* Progress Steps - Mobile Optimized */}
          <div className="w-full">
            {/* Mobile: Compact horizontal scroll */}
            <div className="block sm:hidden">
              <div className="flex items-center space-x-1 overflow-x-auto pb-2 px-1">
                {steps.map((step, index) => (
                  <div key={step.number} className="flex items-center flex-shrink-0 min-w-0">
                    <div className={`flex items-center justify-center w-6 h-6 rounded-full ${
                      currentStep >= step.number
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      {currentStep > step.number ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : (
                        <span className="text-xs font-bold">{step.number}</span>
                      )}
                    </div>
                    <span className={`ml-1 text-xs whitespace-nowrap ${
                      currentStep >= step.number
                        ? 'text-gray-900 dark:text-white font-medium'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.title}
                    </span>
                    {index < steps.length - 1 && (
                      <div className={`w-2 h-px mx-1 ${
                        currentStep > step.number
                          ? 'bg-primary-600'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop: Full layout */}
            <div className="hidden sm:block">
              <div className="flex items-center space-x-2 md:space-x-4 justify-center lg:justify-start">
                {steps.map((step, index) => (
                  <div key={step.number} className="flex items-center flex-shrink-0">
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                      currentStep >= step.number
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400'
                    }`}>
                      {currentStep > step.number ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <step.icon className="w-4 h-4" />
                      )}
                    </div>
                    <span className={`ml-2 text-sm whitespace-nowrap ${
                      currentStep >= step.number
                        ? 'text-gray-900 dark:text-white font-medium'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.title}
                    </span>
                    {index < steps.length - 1 && (
                      <div className={`w-4 md:w-8 h-px mx-2 md:mx-4 ${
                        currentStep > step.number
                          ? 'bg-primary-600'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 sm:p-4 lg:p-6">
              {/* Step 1: Review Item */}
              {currentStep === 1 && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Review Item
                  </h2>
                  
                  <div className="flex space-x-4">
                    <img
                      src={listing?.imageURLs?.[0] || '/placeholder-image.jpg'}
                      alt={listing?.title || 'Item'}
                      className="w-24 h-24 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {listing?.title || 'Loading...'}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {listing?.description ? `${listing.description.substring(0, 100)}...` : 'Loading description...'}
                      </p>
                      <div className="mt-2">
                        <span className="text-lg font-semibold text-primary-600">
                          ${checkoutAction.price}
                        </span>
                        {checkoutAction.type === 'rent' && checkoutAction.period && (
                          <span className="text-sm text-gray-500 ml-1">
                            / {checkoutAction.period.replace('ly', '')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={handleNext}
                      className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Step 2: Shipping Address (only for mail delivery) */}
              {calculateTotal.deliveryMethod === 'mail' && currentStep === 2 && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Shipping Address
                  </h2>

                  <AddressManagement
                    onAddressSelect={handleAddressSelect}
                    selectedAddressId={selectedAddress?.id}
                    showSelection={true}
                  />

                  {/* Shipping Rate Selection */}
                  {selectedAddress && listing?.shippingOptions?.model === 'shippo' && (
                    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                        🚚 Shipping Options
                      </h4>

                      {isLoadingShippingRates ? (
                        <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                          <Loader className="w-4 h-4 animate-spin" />
                          <span>Loading shipping rates...</span>
                        </div>
                      ) : shippingRatesError ? (
                        <div className="text-red-600 dark:text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 inline mr-1" />
                          {shippingRatesError}
                        </div>
                      ) : shippingRates.length > 0 ? (
                        <div className="space-y-2">
                          {shippingRates.slice(0, 3).map((rate) => (
                            <label
                              key={rate.rateId}
                              className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                                selectedShippingRate?.rateId === rate.rateId
                                  ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                                  : 'border-gray-200 dark:border-gray-600 hover:border-primary-300'
                              }`}
                            >
                              <div className="flex items-center">
                                <input
                                  type="radio"
                                  name="shippingRate"
                                  value={rate.rateId}
                                  checked={selectedShippingRate?.rateId === rate.rateId}
                                  onChange={() => setSelectedShippingRate(rate)}
                                  className="sr-only"
                                />
                                <div>
                                  <div className="font-medium text-gray-900 dark:text-white">
                                    {rate.carrier} {rate.service}
                                  </div>
                                  <div className="text-sm text-gray-500 dark:text-gray-400">
                                    Estimated delivery: {rate.estimatedDays} {rate.estimatedDays === 1 ? 'day' : 'days'}
                                  </div>
                                </div>
                              </div>
                              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                                ${rate.amount.toFixed(2)}
                              </div>
                            </label>
                          ))}
                          {shippingRates.length > 3 && (
                            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
                              Showing top 3 options
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="text-gray-500 dark:text-gray-400 text-sm">
                          No shipping rates available
                        </div>
                      )}
                    </div>
                  )}

                  <div className="mt-6 flex justify-between">
                    <button
                      onClick={handleBack}
                      className="flex items-center px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back
                    </button>
                    <button
                      onClick={handleNext}
                      disabled={!selectedAddress || (listing?.shippingOptions?.model === 'shippo' && !selectedShippingRate)}
                      className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Continue
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Order Confirmation Step */}
              {((calculateTotal.deliveryMethod === 'mail' && currentStep === 3) ||
                (calculateTotal.deliveryMethod === 'in_person' && currentStep === 2)) && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Order Confirmation
                  </h2>
                  
                  {/* Delivery Method Info */}
                  <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      📦 Delivery Method
                    </h4>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {calculateTotal.deliveryMethod === 'in_person' ? (
                        <div>
                          <p className="font-medium text-green-600 dark:text-green-400">In-Person Pickup (Free)</p>
                          <p className="mt-1">You'll coordinate pickup directly with the seller. We recommend meeting in a public place like a campus library or dining hall for safety.</p>
                        </div>
                      ) : (
                        <div>
                          <p className="font-medium text-blue-600 dark:text-blue-400">Mail Delivery</p>
                          <p className="mt-1">
                            Item will be shipped to your address.
                            {calculateTotal.shippingPaidBy === 'seller' ? ' Shipping cost included by seller.' : ' Shipping fee applies.'}
                          </p>
                          {selectedShippingRate && calculateTotal.shippingPaidBy === 'buyer' && (
                            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                              <strong>Shipping:</strong> {selectedShippingRate.carrier} {selectedShippingRate.service}
                              ({selectedShippingRate.estimatedDays} {selectedShippingRate.estimatedDays === 1 ? 'day' : 'days'})
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Order Summary */}
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Item Price</span>
                      <span className="text-gray-900 dark:text-white">${formatPrice(pricing.basePrice)}</span>
                    </div>
                    {calculateTotal.deliveryMethod === 'mail' && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">
                          Shipping {calculateTotal.shippingPaidBy === 'seller' ? '(Included)' : ''}
                        </span>
                        <span className="text-gray-900 dark:text-white">
                          {calculateTotal.shippingPaidBy === 'seller' ? 'Free' : `$${formatPrice(pricing.shippingFee)}`}
                        </span>
                      </div>
                    )}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <div className="flex justify-between font-semibold">
                        <span className="text-gray-900 dark:text-white">Total</span>
                        <span className="text-gray-900 dark:text-white">${formatPrice(pricing.total)}</span>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      * Stripe tax may be added at checkout based on your location
                    </div>
                  </div>

                  {/* Shipping Address Summary - Only for mail delivery */}
                  {calculateTotal.deliveryMethod === 'mail' && selectedAddress && (
                    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        Shipping To:
                      </h4>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <p>{selectedAddress.fullName}</p>
                        <p>{selectedAddress.line1}</p>
                        {selectedAddress.line2 && <p>{selectedAddress.line2}</p>}
                        <p>{selectedAddress.city}, {selectedAddress.state} {selectedAddress.zipCode}</p>
                        <p>{selectedAddress.phone}</p>
                      </div>
                    </div>
                  )}

                  <div className="mt-6 flex justify-between">
                    <button
                      onClick={handleBack}
                      className="flex items-center px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back
                    </button>
                    <button
                      onClick={handleNext}
                      className="flex items-center px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      Proceed to Payment
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              )}

              {/* Secure Checkout Step */}
              {((calculateTotal.deliveryMethod === 'mail' && currentStep === 4) ||
                (calculateTotal.deliveryMethod === 'in_person' && currentStep === 3)) && (
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                    Secure Checkout
                  </h2>
                  
                  <div className="text-center py-8">
                    <CreditCard className="w-16 h-16 text-primary-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Ready to Complete Your Purchase
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      You'll be redirected to Stripe's secure checkout to complete your payment.
                    </p>
                    
                    {error && (
                      <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                        <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
                      </div>
                    )}

                    <div className="flex justify-between">
                      <button
                        onClick={handleBack}
                        disabled={isProcessing}
                        className="flex items-center px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                      </button>
                      <button
                        onClick={handleCheckout}
                        disabled={isProcessing}
                        className="flex items-center px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isProcessing ? (
                          <>
                            <Loader className="w-4 h-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            Complete Purchase - ${pricing.total}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar - Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 sm:p-4 lg:p-6 lg:sticky lg:top-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Order Summary
              </h3>
              
              <div className="flex space-x-3 mb-4">
                <img
                  src={listing?.imageURLs?.[0] || '/placeholder-image.jpg'}
                  alt={listing?.title || 'Item'}
                  className="w-16 h-16 object-cover rounded-lg"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                    {listing?.title || 'Loading...'}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {checkoutAction.type === 'buy' && 'Purchase'}
                    {checkoutAction.type === 'rent' && `Rent (${checkoutAction.period})`}
                    {checkoutAction.type === 'bid' && 'Auction Bid'}
                  </p>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Price</span>
                  <span className="text-gray-900 dark:text-white">${formatPrice(pricing.basePrice)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Shipping</span>
                  <span className="text-gray-900 dark:text-white">${formatPrice(pricing.shippingFee)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
                  <span className="text-gray-900 dark:text-white">${formatPrice(pricing.subtotal)}</span>
                </div>

                {/* Wallet Credit Section */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                  <WalletCreditSelector
                    walletBalance={walletBalance}
                    itemPrice={pricing.basePrice}
                    shippingFee={pricing.shippingFee}
                    onCreditChange={setAppliedWalletCredit}
                    isLoading={isLoadingWallet}
                    disabled={isProcessing}
                  />
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-gray-900 dark:text-white">Total to Pay</span>
                    <span className="text-gray-900 dark:text-white">${formatPrice(pricing.total)}</span>
                  </div>
                </div>
                <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  * Tax may be added at checkout
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedCheckout;
