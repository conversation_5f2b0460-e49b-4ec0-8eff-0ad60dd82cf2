import { useState, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Order, Wallet } from '../firebase/types';
import { httpsCallable } from 'firebase/functions';
import { doc, getDoc, collection, query, where, getDocs, updateDoc } from 'firebase/firestore';
import { db, functions } from '../firebase/config';

interface CheckoutOrderDetails {
  type: 'buy' | 'rent' | 'bid';
  price?: number;
  period?: 'weekly' | 'monthly';
  bidAmount?: number;
  appliedWalletCredit?: number;
  shippingAddress?: {
    fullName: string;
    phone: string;
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

interface CheckoutSessionResponse {
  sessionId?: string;
  sessionUrl?: string;
  paidWithWallet?: boolean;
  walletAmountUsed?: number;
  orderId: string;
  message?: string;
  finalStripeAmount?: number;
  originalTotal?: number;
}

interface UseStripeCheckoutReturn {
  createCheckoutSession: (listingId: string, useWalletBalance?: boolean, orderDetails?: CheckoutOrderDetails) => Promise<CheckoutSessionResponse>;
  releaseFundsWithCode: (orderId: string, secretCode: string) => Promise<boolean>;
  createConnectAccount: (accountType: 'student' | 'merchant') => Promise<{ accountId: string; accountLinkUrl: string }>;
  generateShippingLabel: (orderId: string) => Promise<{ labelUrl: string; trackingNumber: string }>;
  markDeliveryCompleted: (orderId: string) => Promise<void>;
  requestReturn: (orderId: string, reason: string, evidence?: File | null) => Promise<void>;
  getWalletBalance: () => Promise<number>;
  getOrderById: (orderId: string) => Promise<Order | null>;
  getOrdersByBuyer: () => Promise<Order[]>;
  getOrdersBySeller: () => Promise<Order[]>;
  isLoading: boolean;
  error: string | null;
}

export const useStripeCheckout = (): UseStripeCheckoutReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { currentUser: user } = useAuth();

  /**
   * Creates a Stripe Checkout Session
   */
  const createCheckoutSession = async (listingId: string, useWalletBalance: boolean = false, orderDetails?: CheckoutOrderDetails) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        console.error('No user found - user must be logged in to checkout');
        throw new Error('You must be logged in to checkout');
      }

      console.log('Creating checkout session for listing:', listingId);
      console.log('User UID:', user.uid);
      console.log('User object:', user);

      // Get the user's ID token for authentication
      const idToken = await user.getIdToken();

      // Use the correct Firebase Functions URL for the Express app
      const functionUrl = `https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi/create-checkout-session`;

      console.log('Authorization header will be:', `Bearer ${idToken.substring(0, 20)}...`);

      const requestBody = {
        listingId,
        buyerId: user.uid,
        useWalletBalance,
        orderDetails
      };

      console.log('=== STRIPE CHECKOUT HOOK DEBUG ===');
      console.log('DEBUG: useStripeCheckout - listingId:', listingId);
      console.log('DEBUG: useStripeCheckout - listingId type:', typeof listingId);
      console.log('DEBUG: useStripeCheckout - listingId length:', listingId?.length);
      console.log('DEBUG: useStripeCheckout - listingId JSON:', JSON.stringify(listingId));
      console.log('DEBUG: useStripeCheckout - buyerId:', user.uid);
      console.log('DEBUG: useStripeCheckout - orderDetails:', JSON.stringify(orderDetails, null, 2));
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}` // Send actual Firebase ID token
        },
        body: JSON.stringify(requestBody)
      });

      console.log('Checkout session response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Checkout session error response:', errorText);

        // Parse error response if it's JSON
        let errorMessage = `Failed to create checkout session: ${response.status} ${response.statusText}`;
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          // If not JSON, use the raw error text
          if (errorText) {
            errorMessage = errorText;
          }
        }

        throw new Error(errorMessage);
      }

      const data = await response.json() as CheckoutSessionResponse;
      console.log('🔍 Raw checkout session response:', data);
      console.log('🔍 Response type:', typeof data);
      console.log('🔍 Response keys:', Object.keys(data));
      console.log('🔍 paidWithWallet value:', data.paidWithWallet);
      console.log('🔍 sessionId value:', data.sessionId);
      console.log('🔍 sessionUrl value:', data.sessionUrl);

      // Handle wallet-only payments (no Stripe session needed)
      if (data.paidWithWallet) {
        console.log('✅ Order paid with wallet balance only:', data);
        return data;
      }

      // Handle regular Stripe payments
      if (!data.sessionId || !data.sessionUrl) {
        console.log('❌ Missing session data - sessionId:', data.sessionId, 'sessionUrl:', data.sessionUrl);
        throw new Error('Invalid checkout session response - missing sessionId or sessionUrl');
      }

      return data;
    } catch (error: unknown) {
      console.error('Checkout session error:', error);
      let errorMessage = 'Failed to create checkout session';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide user-friendly error messages
        if (errorMessage.includes('not authenticated') || errorMessage.includes('Unauthorized')) {
          errorMessage = 'Please sign in to complete your purchase.';
        } else if (errorMessage.includes('Listing not found')) {
          errorMessage = 'This item is no longer available.';
        } else if (errorMessage.includes('Failed to fetch')) {
          errorMessage = 'Unable to connect to payment processor. Please check your internet connection and try again.';
        }
      }

      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Releases funds to the seller using the secret code
   */
  const releaseFundsWithCode = async (orderId: string, secretCode: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to release funds');
      }

      const releaseFundsFn = httpsCallable(functions, 'releaseEscrowWithCode');
      const response = await releaseFundsFn({
        orderId,
        secretCode
      });

      const data = response.data as { success: boolean };
      return data.success;
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to release funds');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Mark in-person delivery as completed (seller function)
   */
  const markDeliveryCompleted = async (orderId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to mark delivery as completed');
      }

      const markDeliveryFn = httpsCallable(functions, 'markDeliveryCompleted');
      const response = await markDeliveryFn({ orderId });

      const data = response.data as { success: boolean };
      return data.success;
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to mark delivery as completed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Request return (buyer function)
   */
  const requestReturn = async (orderId: string, reason?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to request a return');
      }

      const requestReturnFn = httpsCallable(functions, 'requestReturn');
      const response = await requestReturnFn({ orderId, reason });

      const data = response.data as { success: boolean };
      return data.success;
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to request return');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Creates a Stripe Connect account for the user
   */
  const createConnectAccount = async (accountType: 'student' | 'merchant') => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to create a Connect account');
      }

      const createConnectAccountFn = httpsCallable(functions, 'stripeApi-create-connect-account');
      const response = await createConnectAccountFn({
        accountType
      });

      const data = response.data as { accountId: string; accountLinkUrl: string };
      return data;
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to create Connect account');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Gets the user's wallet balance
   */
  const getWalletBalance = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to get wallet balance');
      }

      const walletDoc = await getDoc(doc(db, 'wallets', user.uid));
      
      if (walletDoc.exists()) {
        const wallet = walletDoc.data() as Wallet;
        return wallet.balance;
      }
      
      return 0; // No wallet exists yet
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to get wallet balance');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Gets an order by ID
   */
  const getOrderById = async (orderId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to get order details');
      }

      const orderDoc = await getDoc(doc(db, 'orders', orderId));
      
      if (orderDoc.exists()) {
        const order = orderDoc.data() as Order;
        
        // Verify that the user is either the buyer or seller
        if (order.buyerId !== user.uid && order.sellerId !== user.uid) {
          throw new Error('You do not have permission to view this order');
        }
        
        return order;
      }
      
      return null;
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to get order details');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Gets all orders where the user is the buyer
   */
  const getOrdersByBuyer = useCallback(async () => {
    console.log('🔍 getOrdersByBuyer called for user:', user?.uid);
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        console.log('❌ No user found in getOrdersByBuyer');
        throw new Error('You must be logged in to get your orders');
      }

      console.log('📋 Querying orders for buyer:', user.uid);
      const ordersQuery = query(
        collection(db, 'orders'),
        where('buyerId', '==', user.uid)
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders: Order[] = [];

      ordersSnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order);
      });

      console.log(`✅ Found ${orders.length} buyer orders`);
      return orders;
    } catch (error: unknown) {
      console.error('❌ Error in getOrdersByBuyer:', error);
      setError(error instanceof Error ? error.message : 'Failed to get your orders');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Gets all orders where the user is the seller
   */
  const getOrdersBySeller = useCallback(async () => {
    console.log('🔍 getOrdersBySeller called for user:', user?.uid);
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        console.log('❌ No user found in getOrdersBySeller');
        throw new Error('You must be logged in to get your sales');
      }

      console.log('📋 Querying orders for seller:', user.uid);
      const ordersQuery = query(
        collection(db, 'orders'),
        where('sellerId', '==', user.uid)
      );

      const ordersSnapshot = await getDocs(ordersQuery);
      const orders: Order[] = [];

      ordersSnapshot.forEach((doc) => {
        orders.push({ id: doc.id, ...doc.data() } as Order);
      });

      console.log(`✅ Found ${orders.length} seller orders`);
      return orders;
    } catch (error: unknown) {
      console.error('❌ Error in getOrdersBySeller:', error);
      setError(error instanceof Error ? error.message : 'Failed to get your sales');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  /**
   * Generates a shipping label for an order
   */
  const generateShippingLabel = async (orderId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to generate a shipping label');
      }

      const generateLabelFn = httpsCallable(functions, 'stripeApi-generate-shipping-label');
      const response = await generateLabelFn({
        orderId
      });

      return response.data as { labelUrl: string; trackingNumber: string };
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to generate shipping label');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Marks an order as delivered (for sellers)
   */
  const markDeliveryCompleted = async (orderId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to mark delivery as completed');
      }

      const markDeliveryFn = httpsCallable(functions, 'markDeliveryCompleted');
      await markDeliveryFn({ orderId });
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to mark delivery as completed');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Requests a return for an order (for buyers)
   */
  const requestReturn = async (orderId: string, reason: string, evidence?: File | null) => {
    setIsLoading(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to request a return');
      }

      // For now, we'll update the order document directly
      // In a production app, you might want to use a Cloud Function for this
      const orderRef = doc(db, 'orders', orderId);
      const orderDoc = await getDoc(orderRef);

      if (!orderDoc.exists()) {
        throw new Error('Order not found');
      }

      const orderData = orderDoc.data() as Order;

      // Verify user is the buyer
      if (orderData.buyerId !== user.uid) {
        throw new Error('You can only request returns for your own orders');
      }

      // Update the order with return request
      await updateDoc(orderRef, {
        returnRequest: {
          reason,
          requestedAt: new Date(),
          requestedBy: user.uid,
          status: 'pending',
          evidence: evidence ? evidence.name : null // In production, upload file to storage
        },
        updatedAt: new Date()
      });
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : 'Failed to request return');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createCheckoutSession,
    releaseFundsWithCode,
    markDeliveryCompleted,
    requestReturn,
    createConnectAccount,
    generateShippingLabel,
    getWalletBalance,
    getOrderById,
    getOrdersByBuyer,
    getOrdersBySeller,
    isLoading,
    error
  };
};