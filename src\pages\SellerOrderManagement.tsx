import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  CheckCircle, 
  MessageCircle,
  AlertCircle,
  Calendar,
  DollarSign,
  User,
  MapPin,
  Download,
  Send,
  Clock
} from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';
import { formatPrice } from '../utils/priceUtils';
import toast from 'react-hot-toast';

const SellerOrderManagement: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { getOrderById, markDeliveryCompleted, generateShippingLabel, isLoading: checkoutLoading } = useStripeCheckout();

  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMarkingDelivered, setIsMarkingDelivered] = useState(false);
  const [isGeneratingLabel, setIsGeneratingLabel] = useState(false);

  useEffect(() => {
    if (orderId) {
      loadOrder();
    }
  }, [orderId]);

  const loadOrder = async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const orderData = await getOrderById(orderId);
      if (orderData) {
        // Verify user is the seller
        if (orderData.sellerId !== currentUser?.uid) {
          setError('You do not have permission to view this order');
          return;
        }
        setOrder(orderData);
      } else {
        setError('Order not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load order');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkDelivered = async () => {
    if (!orderId) return;

    setIsMarkingDelivered(true);
    try {
      await markDeliveryCompleted(orderId);
      toast.success('Order marked as delivered! 3-day countdown started.');
      await loadOrder(); // Refresh order data
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to mark as delivered');
    } finally {
      setIsMarkingDelivered(false);
    }
  };

  const handleGenerateLabel = async () => {
    if (!orderId) return;

    setIsGeneratingLabel(true);
    try {
      const result = await generateShippingLabel(orderId);
      toast.success('Shipping label generated successfully!');
      await loadOrder(); // Refresh order data
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Failed to generate shipping label');
    } finally {
      setIsGeneratingLabel(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_payment':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'payment_completed':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'delivered':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'returned':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const canMarkDelivered = order && order.status === 'payment_completed' && !order.deliveredAt;
  const canGenerateLabel = order && order.status === 'payment_completed' && !order.trackingInfo;
  const isAwaitingConfirmation = order && order.status === 'delivered' && !order.completedAt;

  // Calculate auto-release date (3 days after delivery marked)
  const getAutoReleaseDate = () => {
    if (order?.deliveredAt) {
      const deliveryDate = order.deliveredAt.toDate();
      const autoReleaseDate = new Date(deliveryDate);
      autoReleaseDate.setDate(autoReleaseDate.getDate() + 3);
      return autoReleaseDate;
    }
    return null;
  };

  const autoReleaseDate = getAutoReleaseDate();
  const isAutoReleaseActive = autoReleaseDate && new Date() < autoReleaseDate;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error</h3>
                <p className="text-red-700 dark:text-red-300">{error || 'Order not found'}</p>
              </div>
            </div>
            <div className="mt-4">
              <button
                onClick={() => navigate('/orders')}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
              >
                Back to Orders
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => navigate('/orders')}
            className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors mr-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Orders
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Order #{order.id.slice(-8)}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">Manage your sale</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Item Details */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Item Details</h2>
              <div className="flex items-start space-x-4">
                <img
                  src={order.listingImage || '/placeholder-image.jpg'}
                  alt={order.title}
                  className="w-20 h-20 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">{order.title}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{order.description}</p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Category: {order.category}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600 dark:text-green-400">
                    You receive: ${formatPrice(order.sellerAmount || order.amount)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Item price: ${formatPrice(order.amount)}
                  </p>
                </div>
              </div>
            </div>

            {/* Shipping Actions */}
            {canGenerateLabel && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Shipping</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Generate a shipping label to send the item to the buyer.
                </p>
                <button
                  onClick={handleGenerateLabel}
                  disabled={isGeneratingLabel}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {isGeneratingLabel ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Download className="w-4 h-4 mr-2" />
                  )}
                  Generate Shipping Label
                </button>
              </div>
            )}

            {/* Tracking Info */}
            {order.trackingInfo && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tracking Information</h2>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Tracking Number:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.trackingNumber}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Carrier:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{order.trackingInfo.carrier}</span>
                  </div>
                  {order.trackingInfo.trackingUrl && (
                    <div className="pt-2">
                      <a
                        href={order.trackingInfo.trackingUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                      >
                        <Truck className="w-4 h-4 mr-2" />
                        Track Package
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Delivery Confirmation */}
            {canMarkDelivered && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Mark as Delivered</h2>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Check this when you have delivered the item to the buyer. This will start a 3-day countdown for the buyer to confirm receipt.
                </p>
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="delivered"
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleMarkDelivered();
                      }
                    }}
                    disabled={isMarkingDelivered}
                    className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label htmlFor="delivered" className="text-sm font-medium text-gray-900 dark:text-white">
                    I have delivered the item
                  </label>
                  {isMarkingDelivered && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
                  )}
                </div>
              </div>
            )}

            {/* Auto-Release Status */}
            {isAwaitingConfirmation && (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6">
                <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Awaiting Buyer Confirmation
                </h2>
                <p className="text-blue-800 dark:text-blue-200 mb-4">
                  The buyer has {autoReleaseDate ? Math.ceil((autoReleaseDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 3} days 
                  to confirm delivery using the secret code. If they don't confirm by{' '}
                  <strong>{autoReleaseDate?.toLocaleDateString()}</strong>, payment will be automatically released to you.
                </p>
                <div className="bg-blue-100 dark:bg-blue-800/50 rounded-lg p-4">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Secret Code:</strong> {order.secretCode}
                  </p>
                  <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                    Share this code with the buyer to confirm delivery.
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Buyer Information */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <User className="w-5 h-5 mr-2" />
                Buyer Information
              </h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Buyer ID:</span>
                  <p className="font-medium text-gray-900 dark:text-white">{order.buyerId.slice(-8)}</p>
                </div>
                {order.shippingAddress && (
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Shipping Address:</span>
                    <div className="font-medium text-gray-900 dark:text-white">
                      <p>{order.shippingAddress.fullName}</p>
                      <p>{order.shippingAddress.line1}</p>
                      {order.shippingAddress.line2 && <p>{order.shippingAddress.line2}</p>}
                      <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}</p>
                      <p>{order.shippingAddress.country}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Earnings Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Earnings Summary</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Item Price:</span>
                  <span className="text-gray-900 dark:text-white">${formatPrice(order.amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Platform Fee:</span>
                  <span className="text-red-600 dark:text-red-400">-${formatPrice(order.platformFee || 0)}</span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between font-semibold">
                    <span className="text-gray-900 dark:text-white">You Receive:</span>
                    <span className="text-green-600 dark:text-green-400">
                      ${formatPrice(order.sellerAmount || order.amount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
              <div className="space-y-3">
                <Link
                  to={`/messages/${order.buyerId}`}
                  className="w-full flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Chat with Buyer
                </Link>
                <Link
                  to="/orders"
                  className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  View All Orders
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerOrderManagement;
